import re
from bs4 import BeautifulSoup
from datetime import datetime, <PERSON><PERSON><PERSON>

def convert_to_edt(utc_time_str):
    """Converts a UTC timestamp string to a formatted EDT string."""
    utc_time = datetime.fromisoformat(utc_time_str.replace('Z', '+00:00'))
    edt_time = utc_time - timedelta(hours=4)
    return edt_time.strftime('%Y%m%d%H%M')

def extract_reddit_post_info(html_content):
    soup = BeautifulSoup(html_content, 'lxml')
    
    # --- Extract Post Information ---
    post_element = soup.find('shreddit-post')
    if not post_element:
        return "No post found."

    post_title = post_element.get('post-title', 'No Title')
    post_url_slug = post_element.get('permalink', '')
    post_url_match = re.match(r'(/r/[^/]+/comments/[^/]+/)', post_url_slug)
    post_url = f"https://www.reddit.com{post_url_match.group(1)}" if post_url_match else ""
    
    created_timestamp_str = post_element.get('created-timestamp', '')
    formatted_post_time = convert_to_edt(created_timestamp_str) if created_timestamp_str else ""

    output = [f"- {formatted_post_time} - [{post_title}]({post_url})"]
        
    return "\n".join(output)

import pyperclip

if __name__ == "__main__":
    try:
        html_content = pyperclip.paste()
        
        if not html_content:
            print("Clipboard is empty.")
        else:
            result = extract_reddit_post_info(html_content)
            pyperclip.copy(result)
            print(result)
            print("\nResults copied to clipboard.")
            
    except Exception as e:
        print(f"An error occurred: {e}")
