import tkinter as tk
from tkinter import filedialog, messagebox
import re
import os

def process_file(file_path):
    """Processes a single file to remove duplicate lines, overwriting the original."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        seen_timestamps = set()
        unique_lines = []
        timestamp_pattern = re.compile(r"^- \d{12}")

        for line in lines:
            match = timestamp_pattern.search(line)
            if match:
                timestamp = match.group(0)
                if timestamp not in seen_timestamps:
                    seen_timestamps.add(timestamp)
                    unique_lines.append(line)
            else:
                # Keep lines that don't have the timestamp format
                unique_lines.append(line)

        if len(lines) > len(unique_lines):
            # Write the unique lines back to the file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(unique_lines)
            return len(lines) - len(unique_lines)
        return 0
    except Exception as e:
        messagebox.showerror("Error", f"An error occurred while processing {os.path.basename(file_path)}:\n{e}")
        return -1

def main():
    """
    Main function to handle file selection and processing.
    Allows for a hardcoded list of paths or a file dialog for user selection.
    """
    # --- HARDCODED PATHS ---
    # Leave this list empty to use the file selection dialog.
    # You can provide a single path as a string or multiple paths in the list.
    # Example:
    # hardcoded_paths = r"C:\path\to\your\file.txt"
    # hardcoded_paths = [r"C:\path\to\file1.txt", r"C:\path\to\file2.txt"]
    hardcoded_paths = []

    files_to_process = []
    if hardcoded_paths:
        if isinstance(hardcoded_paths, str):
            files_to_process.append(hardcoded_paths)
        elif isinstance(hardcoded_paths, list):
            files_to_process.extend(hardcoded_paths)
    else:
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        file_path = filedialog.askopenfilename(
            title="Select a file to remove duplicate lines from",
            filetypes=(("Text files", "*.txt"), ("All files", "*.*"))
        )
        if file_path:
            files_to_process.append(file_path)
        else:
            # User cancelled the dialog
            return

    total_removed = 0
    processed_files_count = 0
    processed_file_names = []

    for file_path in files_to_process:
        if not os.path.exists(file_path):
            messagebox.showwarning("File Not Found", f"The file was not found and will be skipped:\n{file_path}")
            continue
        
        removed_count = process_file(file_path)
        if removed_count > 0:
            total_removed += removed_count
            processed_files_count += 1
            processed_file_names.append(os.path.basename(file_path))
        elif removed_count == 0:
            processed_files_count += 1 # Still counts as processed
            processed_file_names.append(os.path.basename(file_path))


    if total_removed > 0:
        summary_message = (
            f"Successfully processed {processed_files_count} file(s).\n"
            f"Removed a total of {total_removed} duplicate lines from:\n\n"
            f"{', '.join(processed_file_names)}"
        )
        messagebox.showinfo("Success", summary_message)
    elif processed_files_count > 0:
        messagebox.showinfo("No Duplicates Found", f"No duplicate lines were found in the {processed_files_count} selected file(s).")

if __name__ == "__main__":
    main()
