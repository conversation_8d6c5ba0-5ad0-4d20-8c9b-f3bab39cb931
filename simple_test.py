#!/usr/bin/env python3

# Simple test to analyze MCP Guide.md
import re
import os
from datetime import datetime

def extract_yaml_frontmatter_simple(content):
    """Extract YAML frontmatter from markdown content."""
    if not content.startswith('---'):
        return None, content

    try:
        # Find the end of frontmatter
        end_match = re.search(r'\n---\n', content)
        if not end_match:
            return None, content

        yaml_content = content[3:end_match.start()]
        remaining_content = content[end_match.end():]

        # Simple parsing - look for updated field
        updated_match = re.search(r'^updated:\s*(.*)$', yaml_content, re.MULTILINE)
        updated_value = updated_match.group(1).strip() if updated_match else ""

        return {"updated": updated_value}, remaining_content
    except:
        return None, content

def extract_section_content_simple(content, section_name):
    """Extract content under a specific markdown section."""
    pattern = rf'^# {re.escape(section_name)}\s*$'
    match = re.search(pattern, content, re.MULTILINE)

    if not match:
        return ""

    start_pos = match.end()

    # Find the next section (starting with #)
    next_section = re.search(r'\n# ', content[start_pos:])
    if next_section:
        end_pos = start_pos + next_section.start()
        section_content = content[start_pos:end_pos]
    else:
        section_content = content[start_pos:]

    return section_content.strip()

def extract_log_dates_simple(content):
    """Extract dates in YYYYMMDDHHMM format."""
    # Look for 12-digit numbers that could be dates
    pattern = r'\b(\d{12})\b'
    matches = re.findall(pattern, content)

    dates = []
    for match in matches:
        try:
            # Parse as YYYYMMDDHHMM
            year = int(match[:4])
            month = int(match[4:6])
            day = int(match[6:8])
            hour = int(match[8:10])
            minute = int(match[10:12])

            parsed_date = datetime(year, month, day, hour, minute)
            dates.append(parsed_date)
            print(f"Found date: {match} -> {parsed_date}")
        except Exception as e:
            print(f"Failed to parse date '{match}': {e}")

    return dates

def main():
    file_path = "MCP Guide.md"

    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return

    print(f"Analyzing file: {file_path}")
    print("=" * 50)

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    print(f"File length: {len(content)} characters")

    # Extract frontmatter
    frontmatter, main_content = extract_yaml_frontmatter_simple(content)
    print(f"Frontmatter: {frontmatter}")

    # Check backlog
    backlog_content = extract_section_content_simple(main_content, "Backlog")
    print(f"\nBacklog section content:")
    print(f"'{backlog_content}'")

    backlog_lines = [line.strip() for line in backlog_content.split('\n') if line.strip()]
    print(f"Backlog has {len(backlog_lines)} non-empty lines:")
    for i, line in enumerate(backlog_lines, 1):
        print(f"  {i}. {line}")

    # Check log dates
    print(f"\nLooking for log dates in content...")
    log_dates = extract_log_dates_simple(content)
    print(f"Found {len(log_dates)} log dates")

    # Check updated date
    updated_date = None
    if frontmatter and frontmatter.get('updated'):
        print(f"Updated field value: '{frontmatter['updated']}'")
        if frontmatter['updated'].strip():
            try:
                # Try to parse updated date
                updated_str = frontmatter['updated'].strip()
                if len(updated_str) == 12:
                    year = int(updated_str[:4])
                    month = int(updated_str[4:6])
                    day = int(updated_str[6:8])
                    hour = int(updated_str[8:10])
                    minute = int(updated_str[10:12])
                    updated_date = datetime(year, month, day, hour, minute)
                    print(f"Parsed updated date: {updated_date}")
            except Exception as e:
                print(f"Error parsing updated date: {e}")
        else:
            print("Updated field is empty")
    else:
        print("No updated field found")

    # Final qualification check
    has_backlog = len(backlog_lines) > 0
    has_recent_logs = len(log_dates) > 0 if not updated_date else any(d > updated_date for d in log_dates)

    print(f"\n" + "=" * 50)
    print(f"QUALIFICATION CHECK:")
    print(f"  Has populated backlog: {has_backlog}")
    print(f"  Has recent log entries: {has_recent_logs}")
    print(f"  Updated date: {updated_date or 'None (empty)'}")
    print(f"  Log dates: {[d.strftime('%Y%m%d%H%M') for d in log_dates]}")

    qualifies = has_backlog and has_recent_logs
    print(f"\n  RESULT: File {'QUALIFIES' if qualifies else 'DOES NOT QUALIFY'} for roadmap generation")

    if qualifies:
        print(f"\n✅ This file should be processed by update_roadmaps.py")
    else:
        print(f"\n❌ This file should be skipped by update_roadmaps.py")

if __name__ == "__main__":
    main()
