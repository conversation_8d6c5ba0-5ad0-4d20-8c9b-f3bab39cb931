#!/usr/bin/env python3
"""
Simple test script to debug MCP Guide.md processing
"""

import os
import re
import yaml
from datetime import datetime
from dateutil import parser as date_parser

def extract_yaml_frontmatter(content):
    """Extract YAML frontmatter from markdown content."""
    if not content.startswith('---'):
        return None, content
    
    try:
        # Find the end of frontmatter
        end_match = re.search(r'\n---\n', content)
        if not end_match:
            return None, content
        
        yaml_content = content[3:end_match.start()]
        remaining_content = content[end_match.end():]
        
        frontmatter = yaml.safe_load(yaml_content)
        return frontmatter, remaining_content
    except yaml.YAMLError:
        return None, content

def extract_section_content(content, section_name):
    """Extract content under a specific markdown section."""
    pattern = rf'^# {re.escape(section_name)}\s*$'
    match = re.search(pattern, content, re.MULTILINE)
    
    if not match:
        return ""
    
    start_pos = match.end()
    
    # Find the next section (starting with #)
    next_section = re.search(r'\n# ', content[start_pos:])
    if next_section:
        end_pos = start_pos + next_section.start()
        section_content = content[start_pos:end_pos]
    else:
        section_content = content[start_pos:]
    
    return section_content.strip()

def extract_log_dates(content):
    """Extract all dates from log entries in the content."""
    # Look for common date patterns in logs
    date_patterns = [
        r'\b\d{12}\b',  # YYYYMMDDHHMM (like 202505150738)
        r'\b\d{4}-\d{2}-\d{2}\b',  # YYYY-MM-DD
        r'\b\d{2}/\d{2}/\d{4}\b',  # MM/DD/YYYY
        r'\b\d{1,2}/\d{1,2}/\d{2,4}\b',  # M/D/YY or MM/DD/YYYY
        r'\b\d{8}\b',  # YYYYMMDD
    ]
    
    dates = []
    for pattern in date_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            try:
                if len(match) == 12:  # YYYYMMDDHHMM format
                    parsed_date = datetime.strptime(match, '%Y%m%d%H%M')
                elif len(match) == 8:  # YYYYMMDD format
                    parsed_date = datetime.strptime(match, '%Y%m%d')
                else:
                    parsed_date = date_parser.parse(match, fuzzy=True)
                dates.append(parsed_date)
                print(f"Found date: {match} -> {parsed_date}")
            except Exception as e:
                print(f"Failed to parse date '{match}': {e}")
                continue
    
    return dates

def main():
    file_path = "MCP Guide.md"
    
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return
    
    print(f"Testing file: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"File length: {len(content)} characters")
    
    # Extract frontmatter
    frontmatter, main_content = extract_yaml_frontmatter(content)
    print(f"Frontmatter: {frontmatter}")
    
    # Check backlog
    backlog_content = extract_section_content(main_content, "Backlog")
    print(f"Backlog content: '{backlog_content}'")
    
    backlog_lines = [line.strip() for line in backlog_content.split('\n') if line.strip()]
    print(f"Backlog has {len(backlog_lines)} non-empty lines")
    
    # Check log dates
    log_dates = extract_log_dates(content)
    print(f"Found {len(log_dates)} log dates: {[d.strftime('%Y%m%d%H%M') for d in log_dates]}")
    
    # Check updated date
    updated_date = None
    if frontmatter and 'updated' in frontmatter and frontmatter['updated']:
        try:
            updated_date = date_parser.parse(str(frontmatter['updated']))
            print(f"Updated date: {updated_date}")
        except Exception as e:
            print(f"Error parsing updated date: {e}")
    else:
        print("No updated date (empty or missing)")
    
    # Final qualification check
    has_backlog = len(backlog_lines) > 0
    has_recent_logs = len(log_dates) > 0 if not updated_date else any(d > updated_date for d in log_dates)
    
    print(f"\nQualification check:")
    print(f"  Has populated backlog: {has_backlog}")
    print(f"  Has recent log entries: {has_recent_logs}")
    print(f"  Qualifies for roadmap: {has_backlog and has_recent_logs}")

if __name__ == "__main__":
    main()
