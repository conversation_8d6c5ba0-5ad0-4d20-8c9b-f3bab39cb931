#!/usr/bin/env python3
"""
update_roadmaps.py - Automatically update roadmap sections in markdown files

This script scans markdown files in a folder for:
1. Files with populated "# Backlog" sections
2. Files with log entries dated after the 'updated' date in YAML frontmatter
3. Creates or updates "# Roadmap" sections with AI-generated content

Requirements:
- pip install pyyaml python-dateutil requests
"""

import os
import re
import yaml
from datetime import datetime, timedelta
from dateutil import parser as date_parser
from pathlib import Path
import random
import tkinter as tk
from tkinter import filedialog
import argparse
import requests
import json

# LM Studio configuration
LM_STUDIO_URL = "http://127.0.0.1:7856/v1/chat/completions"
MODEL_NAME = "qwen_qwq-32b"

# Hardcoded random files for context (you can modify these)
RANDOM_BACKLINKED_FILE = "project_notes.md"  # Modify this to an actual file in your system
RANDOM_LINKED_FILE = "resources.md"  # Modify this to an actual file in your system

def browse_folder():
    """Open a folder browser dialog and return the selected path."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    folder_path = filedialog.askdirectory(title="Select folder to scan for markdown files")
    root.destroy()
    return folder_path

def extract_yaml_frontmatter(content):
    """Extract YAML frontmatter from markdown content."""
    if not content.startswith('---'):
        return None, content
    
    try:
        # Find the end of frontmatter
        end_match = re.search(r'\n---\n', content)
        if not end_match:
            return None, content
        
        yaml_content = content[3:end_match.start()]
        remaining_content = content[end_match.end():]
        
        frontmatter = yaml.safe_load(yaml_content)
        return frontmatter, remaining_content
    except yaml.YAMLError:
        return None, content

def extract_section_content(content, section_name):
    """Extract content under a specific markdown section."""
    pattern = rf'^# {re.escape(section_name)}\s*$'
    match = re.search(pattern, content, re.MULTILINE)
    
    if not match:
        return ""
    
    start_pos = match.end()
    
    # Find the next section (starting with #)
    next_section = re.search(r'\n# ', content[start_pos:])
    if next_section:
        end_pos = start_pos + next_section.start()
        section_content = content[start_pos:end_pos]
    else:
        section_content = content[start_pos:]
    
    return section_content.strip()

def has_populated_backlog(content):
    """Check if the file has a populated backlog section."""
    backlog_content = extract_section_content(content, "Backlog")
    if not backlog_content:
        return False
    
    # Remove empty lines and check if there's actual content
    lines = [line.strip() for line in backlog_content.split('\n') if line.strip()]
    return len(lines) > 0

def extract_log_dates(content):
    """Extract all dates from log entries in the content."""
    # Look for common date patterns in logs
    date_patterns = [
        r'\b\d{4}-\d{2}-\d{2}\b',  # YYYY-MM-DD
        r'\b\d{2}/\d{2}/\d{4}\b',  # MM/DD/YYYY
        r'\b\d{1,2}/\d{1,2}/\d{2,4}\b',  # M/D/YY or MM/DD/YYYY
        r'\b\d{4}\d{2}\d{2}\b',  # YYYYMMDD
    ]
    
    dates = []
    for pattern in date_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            try:
                parsed_date = date_parser.parse(match, fuzzy=True)
                dates.append(parsed_date)
            except:
                continue
    
    return dates

def has_recent_log_entries(content, updated_date):
    """Check if there are log entries after the updated date."""
    log_dates = extract_log_dates(content)
    
    if not log_dates:
        return False
    
    # If no updated date, consider any log entry as recent
    if not updated_date:
        return len(log_dates) > 0
    
    # Check if any log date is after the updated date
    for log_date in log_dates:
        if log_date > updated_date:
            return True
    
    return False

def read_context_file(file_path, base_folder):
    """Read a context file if it exists."""
    full_path = os.path.join(base_folder, file_path)
    if os.path.exists(full_path):
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                return f.read()
        except:
            return ""
    return ""

def generate_roadmap_prompt(content, backlinked_content, linked_content):
    """Generate the prompt for AI roadmap creation."""
    prompt = f"""Based on your current context, (conversations, plans, backlog, logs). Determine what is the goal of the user in the current scope. Then make a best guess estimate of what is the "state" of the user. Then construct 5 past tasks you think the user has done and create 5 future tasks that you think the user should pursue. Pay special attention to any tools, training, and frameworks that are used or should be mentioned such ideas with single brackets. Just give a single bullet list giving the estimated goal, state, past tasks and future tasks. Avoid using headings.

Current File Content:
{content}

Backlinked File Content:
{backlinked_content}

Linked File Content:
{linked_content}"""
    
    return prompt

def call_lm_studio_api(prompt):
    """Call LM Studio API to generate roadmap content."""
    try:
        headers = {
            "Content-Type": "application/json"
        }

        data = {
            "model": MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 1000,
            "stream": False
        }

        response = requests.post(LM_STUDIO_URL, headers=headers, json=data, timeout=60)
        response.raise_for_status()

        result = response.json()
        if 'choices' in result and len(result['choices']) > 0:
            return result['choices'][0]['message']['content']
        else:
            return "Error: No response from AI model"

    except requests.exceptions.RequestException as e:
        print(f"Error calling LM Studio API: {e}")
        return f"Error: Failed to connect to LM Studio - {str(e)}"
    except json.JSONDecodeError as e:
        print(f"Error parsing API response: {e}")
        return "Error: Invalid response from AI model"
    except Exception as e:
        print(f"Unexpected error: {e}")
        return f"Error: {str(e)}"

def extract_non_thinking_content(ai_response):
    """Extract content outside of <thinking> tags."""
    # Remove thinking tags and their content
    cleaned = re.sub(r'<thinking>.*?</thinking>', '', ai_response, flags=re.DOTALL)
    return cleaned.strip()

def update_roadmap_section(content, roadmap_content):
    """Add or update the roadmap section in the markdown content."""
    # Check if roadmap section already exists
    roadmap_match = re.search(r'^# Roadmap\s*$', content, re.MULTILINE)
    
    if roadmap_match:
        # Find the next section after roadmap
        start_pos = roadmap_match.end()
        next_section = re.search(r'\n# ', content[start_pos:])
        
        if next_section:
            # Insert before the next section
            insert_pos = start_pos + next_section.start()
            updated_content = (content[:insert_pos] + 
                             f"\n\n{roadmap_content}\n" + 
                             content[insert_pos:])
        else:
            # Append to the end
            updated_content = content + f"\n\n{roadmap_content}\n"
    else:
        # Add new roadmap section at the end
        updated_content = content + f"\n\n# Roadmap\n\n{roadmap_content}\n"
    
    return updated_content

def process_markdown_file(file_path, base_folder):
    """Process a single markdown file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract frontmatter
        frontmatter, main_content = extract_yaml_frontmatter(content)
        
        # Get updated date from frontmatter
        updated_date = None
        if frontmatter and 'updated' in frontmatter:
            try:
                updated_date = date_parser.parse(str(frontmatter['updated']))
            except:
                pass
        
        # Check conditions
        if not has_populated_backlog(main_content):
            return False, "No populated backlog section"
        
        if not has_recent_log_entries(main_content, updated_date):
            return False, "No recent log entries"
        
        # Read context files
        backlinked_content = read_context_file(RANDOM_BACKLINKED_FILE, base_folder)
        linked_content = read_context_file(RANDOM_LINKED_FILE, base_folder)
        
        # Generate roadmap
        prompt = generate_roadmap_prompt(content, backlinked_content, linked_content)
        print(f"  Calling LM Studio API...")
        ai_response = call_lm_studio_api(prompt)
        roadmap_content = extract_non_thinking_content(ai_response)
        
        # Update the file
        updated_content = update_roadmap_section(content, roadmap_content)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        return True, "Successfully updated roadmap"
        
    except Exception as e:
        return False, f"Error processing file: {str(e)}"

def main():
    """Main function to run the script."""
    parser = argparse.ArgumentParser(description='Update roadmap sections in markdown files')
    parser.add_argument('--folder', '-f', help='Folder path to scan (if not provided, will open browser)')
    args = parser.parse_args()
    
    # Get folder path
    if args.folder:
        folder_path = args.folder
    else:
        folder_path = browse_folder()
    
    if not folder_path:
        print("No folder selected. Exiting.")
        return
    
    if not os.path.exists(folder_path):
        print(f"Folder does not exist: {folder_path}")
        return
    
    print(f"Scanning folder: {folder_path}")
    print(f"Using context files: {RANDOM_BACKLINKED_FILE}, {RANDOM_LINKED_FILE}")
    
    # Find all .md files in the top level
    md_files = [f for f in os.listdir(folder_path) 
                if f.endswith('.md') and os.path.isfile(os.path.join(folder_path, f))]
    
    if not md_files:
        print("No markdown files found in the folder.")
        return
    
    print(f"Found {len(md_files)} markdown files")
    
    processed_count = 0
    updated_count = 0
    
    for md_file in md_files:
        file_path = os.path.join(folder_path, md_file)
        print(f"\nProcessing: {md_file}")
        
        success, message = process_markdown_file(file_path, folder_path)
        processed_count += 1
        
        if success:
            updated_count += 1
            print(f"  ✓ {message}")
        else:
            print(f"  - {message}")
    
    print(f"\nSummary:")
    print(f"  Files processed: {processed_count}")
    print(f"  Files updated: {updated_count}")
    print(f"  Files skipped: {processed_count - updated_count}")

if __name__ == "__main__":
    main()
