javascript:(function(){let a=0,b=0;const c=20,d=1000,e=document.createElement('div');e.style.cssText='position:fixed;top:10px;right:10px;background:#ff4500;color:white;padding:10px;border-radius:5px;z-index:10000;font-family:Arial;font-size:14px;',e.textContent='Loading Reddit content... 0%',document.body.appendChild(e);function f(g,h){const i=Math.round(g/h*100);e.textContent=`Loading Reddit content... ${i}% (${g}/${h})`}function j(){return new Promise(g=>{if(b>=c){g();return}b++,f(b,c),window.scrollTo(0,document.body.scrollHeight),setTimeout(()=>{document.querySelectorAll('shreddit-post, [data-testid="post-container"], article').length;j().then(g)},d)})}function k(){window.scrollTo(0,0),setTimeout(()=>{const g=document.documentElement.outerHTML;function h(){const i=document.createElement('textarea');i.value=g,document.body.appendChild(i),i.select(),document.execCommand('copy'),document.body.removeChild(i)}navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(g).then(()=>{e.textContent='Reddit HTML copied to clipboard!',e.style.background='#28a745',setTimeout(()=>document.body.removeChild(e),3000)},()=>{h(),e.textContent='Reddit HTML copied via fallback!',e.style.background='#28a745',setTimeout(()=>document.body.removeChild(e),3000)}):(h(),e.textContent='Reddit HTML copied via fallback!',e.style.background='#28a745',setTimeout(()=>document.body.removeChild(e),3000))},1000)}j().then(()=>{e.textContent='Capturing HTML...',k()})})();
